# Frontend Environment Variables Example
# Copy this file to .env for development or .env.production for production

# Supabase configuration (exposed to frontend)
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Backend API URL for Project Management System
# For development (local):
VITE_API_URL=http://localhost:8000
# For production, configure in Coolify: https://backend.aceleralia.com

# Environment identifier
# Options: development, production
VITE_ENVIRONMENT=development

# IMPORTANT: For production deployment in Coolify
# Configure these variables in Coolify's Environment Variables section:
# - VITE_SUPABASE_URL: Your Supabase project URL
# - VITE_SUPABASE_ANON_KEY: Your Supabase anonymous key
# - VITE_API_URL: Your backend API URL
# - VITE_ENVIRONMENT: production
#
# These will be automatically injected as build arguments during deployment