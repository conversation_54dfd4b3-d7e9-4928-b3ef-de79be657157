/**
 * API Client for Project Management System
 * Centralized HTTP client for all backend communication
 */

/// <reference types="vite/client" />

import { supabase } from '../services/supabaseClient';
// Import types from existing type files
import type {
  Tarea,
  TareaCreate,
  TareaUpdate,
  TareaListResponse,
  TareaMatrix,
  TareaKanbanBoard,
} from '../types/tarea';

import type {
  DashboardData,
  QuickActionsResponse,
  DashboardNotificationsResponse,
} from '../types/dashboard';

import type {
  Proyecto,
  ProyectoCreate,
  ProyectoUpdate,
  ProyectoListResponse,
} from '../types/proyecto';

import type {
  Proceso,
  ProcesoCreate,
  ProcesoUpdate,
  ProcesoListResponse,
} from '../types/proceso';

import type {
  GlobalSearchResponse,
} from '../types/search';

import type {
  ProcesoClienteListResponse,
  ProcesoClienteDetalle,
  ProcesoClienteUpdate,
  TareaClienteListResponse,
  TareaClienteDetalle,
  TareaClienteUpdate,
  ProcesoClienteContadores,
  PersonaResponsable,
  DepartamentoInfo,
} from '../types/proceso_cliente';

import type {
  Empresa,
  EmpresaCreate,
  EmpresaUpdate,
  EmpresaStats,
  EmpresaGeneralDetails,
  EmpresaHallazgosDetails,
  HallazgoDetail,
  EmpresaReunionesDetails,
} from '../types/empresa';

import type {
  Idea,
  IdeaCreate,
  IdeaUpdate,
  IdeasListResponse,
} from '../types/idea';

// Define API-specific types inline
export type ApiParams = Record<string, string | number | boolean | undefined | null>;
export type ApiRequestBody = Record<string, unknown> | FormData | string | null | undefined | object;

export interface ProyectoOption {
  id: string;
  nombre: string;
}

export interface EmpresaOption {
  id: string;
  nombre: string;
}

export interface HealthResponse {
  status: string;
  timestamp: string;
}

export interface Usuario {
  id: string;
  nombre: string;
  email?: string;
  rol?: string;
  activo?: boolean;
  created_at?: string;
  updated_at?: string;
}

// Import centralized API configuration
import { API_BASE_URL } from '../config/api';

/**
 * HTTP Client with automatic error handling and authentication
 */
class ApiClient {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };
  }

  /**
   * Set authentication token for all requests
   */
  setAuthToken(token: string) {
    this.defaultHeaders['Authorization'] = `Bearer ${token}`;
  }

  /**
   * Remove authentication token
   */
  clearAuthToken() {
    delete this.defaultHeaders['Authorization'];
  }

  /**
   * Generic HTTP request method
   */
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;

    // Get current session token from Supabase
    const { data: { session } } = await supabase.auth.getSession();
    const headers = { ...this.defaultHeaders };

    // Add authorization header if user is authenticated
    if (session?.access_token) {
      headers['Authorization'] = `Bearer ${session.access_token}`;
    }

    const config: RequestInit = {
      ...options,
      headers: {
        ...headers,
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, config);
      
      // Handle non-JSON responses (like 204 No Content)
      if (response.status === 204) {
        return {} as T;
      }

      const data = await response.json();

      if (!response.ok) {
        // Handle different error response formats
        let errorMessage = `HTTP error! status: ${response.status}`;

        if (data) {
          if (typeof data === 'string') {
            errorMessage = data;
          } else if (data.detail) {
            errorMessage = data.detail;
          } else if (data.message) {
            errorMessage = data.message;
          } else if (Array.isArray(data) && data.length > 0) {
            // Handle validation errors array
            errorMessage = data.map(err => err.msg || err.message || JSON.stringify(err)).join(', ');
          } else {
            // Fallback to stringify the entire error object
            errorMessage = JSON.stringify(data);
          }
        }

        throw new Error(errorMessage);
      }

      return data;
    } catch (error) {
      console.error(`API Error [${options.method || 'GET'}] ${url}:`, error);
      throw error;
    }
  }

  /**
   * GET request
   */
  async get<T>(endpoint: string, params?: ApiParams): Promise<T> {
    let url = endpoint;

    if (params) {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });

      if (searchParams.toString()) {
        url += `?${searchParams.toString()}`;
      }
    }

    return this.request<T>(url, { method: 'GET' });
  }

  /**
   * POST request
   */
  async post<T>(endpoint: string, data?: ApiRequestBody): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * PUT request
   */
  async put<T>(endpoint: string, data?: ApiRequestBody): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * PATCH request
   */
  async patch<T>(endpoint: string, data?: ApiRequestBody): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * DELETE request
   */
  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }
}

// Create API client instance
const api = new ApiClient(API_BASE_URL);

/**
 * Organized API endpoints by feature
 */
export const apiClient = {
  // Dashboard endpoints
  dashboard: {
    getData: () => api.get<DashboardData>('/dashboard'),
    getQuickActions: () => api.get<QuickActionsResponse>('/dashboard/quick-actions'),
    getNotifications: () => api.get<DashboardNotificationsResponse>('/dashboard/notifications'),
  },

  // Projects endpoints
  proyectos: {
    getAll: (params?: ApiParams) => api.get<ProyectoListResponse>('/proyectos', params),
    getById: (id: string) => api.get<Proyecto>(`/proyectos/${id}`),
    create: (data: ProyectoCreate) => api.post<Proyecto>('/proyectos', data),
    update: (id: string, data: ProyectoUpdate) => api.put<Proyecto>(`/proyectos/${id}`, data),
    delete: (id: string) => api.delete<void>(`/proyectos/${id}`),
    getEstados: () => api.get<{ estados: string[] }>('/proyectos/estados/available'),
    getPrioridades: () => api.get<{ prioridades: string[] }>('/proyectos/prioridades/available'),
  },

  // Processes endpoints
  procesos: {
    getAll: (params?: ApiParams) => api.get<ProcesoListResponse>('/procesos', params),
    getById: (id: string) => api.get<Proceso>(`/procesos/${id}`),
    create: (data: ProcesoCreate) => api.post<Proceso>('/procesos', data),
    update: (id: string, data: ProcesoUpdate) => api.put<Proceso>(`/procesos/${id}`, data),
    delete: (id: string) => api.delete<void>(`/procesos/${id}`),
    getTipos: () => api.get<{ tipos: string[] }>('/procesos/tipos/available'),
    getEstados: () => api.get<{ estados: string[] }>('/procesos/estados/available'),
  },

  // Tasks endpoints
  tareas: {
    getAll: (params?: ApiParams) => api.get<TareaListResponse>('/tareas', params),
    getById: (id: string) => api.get<Tarea>(`/tareas/${id}`),
    create: (data: TareaCreate) => api.post<Tarea>('/tareas', data),
    update: (id: string, data: TareaUpdate) => api.put<Tarea>(`/tareas/${id}`, data),
    delete: (id: string) => api.delete<void>(`/tareas/${id}`),
    getMatrix: (params?: ApiParams) => api.get<TareaMatrix>('/tareas/matrix', params),
    getKanban: (params?: ApiParams) => api.get<TareaKanbanBoard>('/tareas/kanban', params),
    getEstados: () => api.get<{ estados: string[] }>('/tareas/estados/available'),
    getPrioridades: () => api.get<{ prioridades: string[] }>('/tareas/prioridades/available'),
    getUrgencias: () => api.get<{ urgencias: string[] }>('/tareas/urgencias/available'),
    getProyectos: () => api.get<ProyectoOption[]>('/tareas/proyectos/available'),
    getEmpresas: () => api.get<EmpresaOption[]>('/tareas/empresas/available'),
  },

  // Empresas endpoints
  empresas: {
    getAll: (params?: ApiParams) => api.get<Empresa[]>('/empresas', params),
    getById: (id: string) => api.get<Empresa>(`/empresas/${id}`),
    getGeneralDetails: (id: string) => api.get<EmpresaGeneralDetails>(`/empresas/${id}/general-details`),
    getHallazgosDetails: (id: string) => api.get<EmpresaHallazgosDetails>(`/empresas/${id}/hallazgos-details`),
    getHallazgoDetail: (hallazgoId: string) => api.get<HallazgoDetail>(`/empresas/hallazgos/${hallazgoId}`),
    getReunionesDetails: (id: string) => api.get<EmpresaReunionesDetails>(`/empresas/${id}/reuniones-details`),
    getIdeasDetails: (id: string) => api.get<IdeasListResponse>(`/empresas/${id}/ideas-details`),
    create: (data: EmpresaCreate) => api.post<Empresa>('/empresas', data),
    update: (id: string, data: EmpresaUpdate) => api.put<Empresa>(`/empresas/${id}`, data),
    delete: (id: string) => api.delete<void>(`/empresas/${id}`),
    getStats: () => api.get<EmpresaStats>('/empresas/stats'),
    getTiposRelacion: () => api.get<{ tipos: string[] }>('/empresas/tipos-relacion/available'),
  },

  // Search endpoints
  search: {
    global: (query: string, params?: ApiParams) =>
      api.get<GlobalSearchResponse>('/search', { q: query, ...params }),
    quick: (query: string, limit?: number) =>
      api.get<GlobalSearchResponse>('/search/quick', { q: query, limit }),
    suggestions: (query: string, limit?: number) =>
      api.get<string[]>('/search/suggestions', { q: query, limit }),
  },

  // Client processes endpoints
  procesosClientes: {
    getByEmpresa: (empresaId: string, params?: ApiParams) =>
      api.get<ProcesoClienteListResponse>(`/empresas/${empresaId}/procesos_clientes_con_metricas`, params),
    getDetalle: (procesoId: string) =>
      api.get<ProcesoClienteDetalle>(`/procesos_clientes/${procesoId}/detalles_completos`),
    update: (procesoId: string, data: ProcesoClienteUpdate) =>
      api.patch<ProcesoClienteDetalle>(`/procesos_clientes/${procesoId}`, data),
    getContadores: (empresaId: string) =>
      api.get<ProcesoClienteContadores>(`/empresas/${empresaId}/procesos_clientes/contadores`),
    getTareas: (procesoId: string, params?: ApiParams) =>
      api.get<TareaClienteListResponse>(`/procesos_clientes/${procesoId}/tareas`, params),
    getPersonasDisponibles: (empresaId: string) =>
      api.get<{personas: PersonaResponsable[]}>(`/empresas/${empresaId}/personas_disponibles`),
    getDepartamentosDisponibles: (empresaId: string) =>
      api.get<{departamentos: DepartamentoInfo[]}>(`/empresas/${empresaId}/departamentos_disponibles`),
  },

  // Client tasks endpoints
  tareasClientes: {
    getDetalle: (tareaId: string) =>
      api.get<TareaClienteDetalle>(`/tareas_clientes/${tareaId}`),
    update: (tareaId: string, data: TareaClienteUpdate) =>
      api.patch<TareaClienteDetalle>(`/tareas_clientes/${tareaId}`, data),
  },

  // Ideas endpoints
  ideas: {
    getAll: (params?: ApiParams) => api.get<IdeasListResponse>('/ideas', params),
    getById: (id: string) => api.get<Idea>(`/ideas/${id}`),
    create: (data: IdeaCreate) => api.post<Idea>('/ideas', data),
    update: (id: string, data: IdeaUpdate) => api.put<Idea>(`/ideas/${id}`, data),
    delete: (id: string) => api.delete<void>(`/ideas/${id}`),
  },

  usuarios: {
    getAll: (params?: ApiParams) => api.get<Usuario[]>('/usuarios', params),
    getById: (id: string) => api.get<Usuario>(`/usuarios/${id}`),
  },

  // Health check
  health: () => api.get<HealthResponse>('/health'),
};

// Export the raw API client for custom requests
export { api };

// Export auth methods for easy access
export const setAuthToken = (token: string) => api.setAuthToken(token);
export const clearAuthToken = () => api.clearAuthToken();

// Default export
export default apiClient;
